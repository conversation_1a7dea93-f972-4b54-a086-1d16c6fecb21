nameOverride: integrations
fullnameOverride: integrations
replicaCount: 1

image:
  registry: "************.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-integrations-service"
  tag: "ATL-3804-cf166835"

initContainerConfig: {}

imagePullSecrets: []
hostname: ""

imageConfig:
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80

automountServiceAccountToken: true

ingress:
  enabled: false
  annotations: {}
  hosts:
    - host: chart-example.local
      paths: []

  tls: []

alb_ingress:
  enabled: false

virtualService:
  enabled: false

cronjob:
  enabled: false

job:
  enabled: false

## Allow definition of pvc
persistence:
  ##
  ## enable persistance storage
  enabled: false

  ## Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  # storageClass: default
  accessMode: ReadWriteOnce
  ##
  ## Persistant storage size request
  size: 1Gi


ports: |
  - containerPort: 11550
    name: http
    protocol: TCP
  - containerPort: 11560
    name: management
    protocol: TCP
  - containerPort: 8888
    name: jmx
    protocol: TCP

podLabels:
  # allow datadog to inject variables required for APM
  admission.datadoghq.com/enabled: "true"

env:
  SPRING_PROFILES_ACTIVE: ops2
  SQS_QUEUES_RESOLVEDISSUES: dev-integrations-resolved-issues
  SQS_QUEUES_PLATFORMEVENTS: dev-integrations-platform-events
  SQS_QUEUES_INTEGRATIONSRESOLVEDISSUES: dev-integrations-resolved-issues
  CLOUD_AWS_REGION_STATIC: us-east-1
  SRCCLR_SNOWPLOW_ENABLED: 'false'
  SPRING_DATASOURCE_USERNAME: integrations
  SPRING_REDIS_HOST: 'sca-redis-primary'
  SPRING_REDIS_PORT: '6379'
  SPRING_REDIS_TLS_ENABLE: 'false'
  SPRING_REDIS_TLS_VERIFY: 'false'
  PROXY_URL_NOT_ACCESSIBLE: 'true'
  # DataDog variables --------------------------------------------------------------------------
  DD_TAGS: "env:__ENV_NAME__ stack_name:__ENV_BRANCH__"
  DD_SERVICE: "integrations"
  DD_PROFILING_ENABLED: __DD_PROFILING_ENABLED__
  JAVA_TOOL_OPTIONS: "-javaagent:/datadog/dd-java-agent.jar"

valueFrom:
  - name: SPRING_DATASOURCE_URL
    value: ******************************************************************************************************************__
  - name: SPRING_DATASOURCE_PASSWORD
    valueFrom:
      secretKeyRef:
        key: SPRING_DATASOURCE_PASSWORD
        name: integrations-secrets
  - name: SECURITY_PASSWORD
    valueFrom:
      secretKeyRef:
        key: SECURITY_PASSWORD
        name: integrations-secrets
  - name: SECURITY_SALT
    valueFrom:
      secretKeyRef:
        key: SECURITY_SALT
        name: integrations-secrets
  - name: JAVA_OPTS
    value: "-Xms2G -Xmx2G -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false
      -Dcom.sun.management.jmxremote.port=8888 -Dcom.sun.management.jmxremote.ssl=false
      -Dcom.sun.management.jmxremote.local.only=false -Djava.rmi.server.hostname=localhost
      -Djava.security.egd=file:/dev/urandom\n"
  - name: SENTRY_DSN
    value: http://9c7d0b3b432f45c9a48fa2fbf2e16b8e:<EMAIL>/16
readinessProbe: |
  failureThreshold: 3
  httpGet:
    path: "/management/health"
    port: 11560
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
resources:
  limits:
    memory: 2G
  requests:
    memory: 2G

external_secret:
  name: integrations-secrets
  data:
  - key: dev/shared/integrations-secrets
    name: SECURITY_PASSWORD
    property: SECURITY_PASSWORD
  - key: dev/shared/integrations-secrets
    name: SECURITY_SALT
    property: SECURITY_SALT
  - key: dev/shared/integrations-secrets
    name: SPRING_DATASOURCE_PASSWORD
    property: SPRING_DATASOURCE_PASSWORD

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/integrations-stage-dyn-tachyon-serviceaccount
  name: integrations