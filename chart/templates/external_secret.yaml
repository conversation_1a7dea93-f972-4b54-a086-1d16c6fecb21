{{- if .Values.external_secret }}
  {{- $en := include "integrations-svc.fullname" . -}}
  {{- $esName := .Values.external_secret.name | default $en}}
  {{- $secretStoreName := printf "%s-secretstore" $esName }}
---
apiVersion: external-secrets.io/v1
kind: SecretStore
metadata:
  name: {{ $secretStoreName }}
  {{- with .Values.external_secret.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  provider:
    aws:
      service: SecretsManager
      region: {{ .Values.external_secret.region | default "us-east-1" }}
      {{- with .Values.external_secret.roleArn }}
      role: {{ . }}
      {{- end }}
      {{- if .Values.external_secret.auth }}
      auth:
        {{- toYaml .Values.external_secret.auth | nindent 8 }}
      {{- end }}
---
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ $esName }}
  {{- with .Values.external_secret.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }} # Adds the annotations
  {{- end }}
spec:
  secretStoreRef:
    name: {{ $secretStoreName }}
    kind: SecretStore
  target:
    name: {{ $esName }}
    creationPolicy: Owner
  data:
    {{- with .Values.external_secret.data }}
    {{- range . }}
    - secretKey: {{ .name }}
      remoteRef:
        key: {{ .key }}
        {{- if .property }}
        property: {{ .property }}
        {{- end }}
    {{- end }}
    {{- end }}
  {{- end }}
  {{- if .Values.extraExternalSecrets }}
  {{- range $index, $extraSecret := .Values.extraExternalSecrets }} # iterates through the list of externalsecrets defined in the values file
  {{- $extraSecretStoreName := printf "%s-secretstore" .name }}
---
apiVersion: external-secrets.io/v1
kind: SecretStore
metadata:
  name: {{ $extraSecretStoreName }}
  {{- if .annotations }}
  annotations:
    {{- toYaml .annotations | nindent 4 }}
  {{- end }}
spec:
  provider:
    aws:
      service: SecretsManager
      region: {{ .region | default "us-east-1" }}
      {{- with .roleArn }}
      role: {{ . }}
      {{- end }}
      {{- if .auth }}
      auth:
        {{- toYaml .auth | nindent 8 }}
      {{- end }}
---
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ .name }}
  {{- if .annotations }}
  annotations:
    {{- toYaml .annotations | nindent 4 }}
  {{- end }}
spec:
  secretStoreRef:
    name: {{ $extraSecretStoreName }}
    kind: SecretStore
  target:
    name: {{ .name }}
    creationPolicy: Owner
  data:  {{- range .data }} # iterates through all the defined entries of the current externalsecret
    - secretKey: {{ .name }}
      remoteRef:
        key: {{ .key }}
        {{- if .property }}
        property: {{ .property }}
        {{- end }}
  {{- end }}
  {{- end }}
  {{- end }}

