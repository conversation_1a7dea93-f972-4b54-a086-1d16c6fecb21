# External Secrets Operator Migration Guide

This document describes the migration from the old External Secrets Operator API (`kubernetes-client.io/v1`) to the new API (`external-secrets.io/v1`).

## Overview

The External Secrets Operator has been updated to use the new API version `external-secrets.io/v1`. This migration provides better security, more features, and improved maintainability.

## What Changed

### API Version
- **Old**: `apiVersion: kubernetes-client.io/v1`
- **New**: `apiVersion: external-secrets.io/v1`

### Resource Structure
The new API introduces a `SecretStore` resource that separates provider configuration from secret definitions.

#### Old Structure (kubernetes-client.io/v1)
```yaml
apiVersion: kubernetes-client.io/v1
kind: ExternalSecret
metadata:
  name: my-secret
spec:
  backendType: secretsManager
  roleArn: arn:aws:iam::************:role/my-role
  data:
    - key: my-secret-key
      name: MY_SECRET
      property: password
```

#### New Structure (external-secrets.io/v1)
```yaml
apiVersion: external-secrets.io/v1
kind: SecretStore
metadata:
  name: my-secretstore
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-east-1
      role: arn:aws:iam::************:role/my-role
---
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: my-secret
spec:
  secretStoreRef:
    name: my-secretstore
    kind: SecretStore
  target:
    name: my-secret
    creationPolicy: Owner
  data:
    - secretKey: MY_SECRET
      remoteRef:
        key: my-secret-key
        property: password
```

## Helm Chart Changes

### Automatic Migration
The Helm chart automatically handles the migration:

1. **SecretStore Creation**: For each `external_secret` configuration, the chart creates a corresponding `SecretStore`
2. **ExternalSecret Update**: The `ExternalSecret` now references the `SecretStore`
3. **Backward Compatibility**: Existing values.yaml configurations continue to work

### Configuration Options

#### Basic Configuration
```yaml
external_secret:
  name: integrations-secrets
  data:
    - key: dev/shared/integrations-secrets
      name: SECURITY_PASSWORD
      property: SECURITY_PASSWORD
```

#### Advanced Configuration
```yaml
external_secret:
  name: integrations-secrets
  region: us-east-1  # Optional, defaults to us-east-1
  roleArn: arn:aws:iam::************:role/external-secrets-role  # Optional
  auth:  # Optional authentication configuration
    secretRef:
      accessKeyIDSecretRef:
        name: aws-credentials
        key: access-key-id
      secretAccessKeySecretRef:
        name: aws-credentials
        key: secret-access-key
  data:
    - key: dev/shared/integrations-secrets
      name: SECURITY_PASSWORD
      property: SECURITY_PASSWORD
```

## Authentication Methods

### 1. Pod Identity (Recommended)
Uses IAM Roles for Service Accounts (IRSA):

```yaml
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/integrations-serviceaccount
```

### 2. Access Keys
Store AWS credentials in a Kubernetes secret:

```yaml
external_secret:
  auth:
    secretRef:
      accessKeyIDSecretRef:
        name: aws-credentials
        key: access-key-id
      secretAccessKeySecretRef:
        name: aws-credentials
        key: secret-access-key
```

### 3. Assume Role
Assume a specific IAM role:

```yaml
external_secret:
  roleArn: arn:aws:iam::************:role/external-secrets-role
```

## Benefits of the New API

1. **Better Security**: Separation of provider configuration from secret definitions
2. **Reusability**: Multiple ExternalSecrets can share the same SecretStore
3. **Enhanced Features**: Support for more providers and authentication methods
4. **Improved Monitoring**: Better status reporting and error handling
5. **Future-Proof**: Active development and community support

## Migration Checklist

- [ ] Update External Secrets Operator to v0.9.0 or later
- [ ] Deploy updated Helm chart
- [ ] Verify secrets are created correctly
- [ ] Monitor for any authentication issues
- [ ] Update monitoring/alerting if needed

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify IAM roles and policies
   - Check service account annotations
   - Ensure AWS credentials are correct

2. **Secret Not Created**
   - Check SecretStore status: `kubectl get secretstore`
   - Check ExternalSecret status: `kubectl get externalsecret`
   - Review operator logs

3. **Permission Denied**
   - Verify IAM policies include required permissions
   - Check if role assumption is working correctly

### Useful Commands

```bash
# Check SecretStore status
kubectl get secretstore -o wide

# Check ExternalSecret status
kubectl get externalsecret -o wide

# Describe resources for detailed information
kubectl describe secretstore <name>
kubectl describe externalsecret <name>

# Check operator logs
kubectl logs -n external-secrets-system deployment/external-secrets
```

## Support

For issues related to the External Secrets Operator, refer to:
- [External Secrets Operator Documentation](https://external-secrets.io/)
- [GitHub Repository](https://github.com/external-secrets/external-secrets)
- [Community Slack](https://kubernetes.slack.com/channels/external-secrets)
