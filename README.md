# SCA integrations service Helm Chart

For release process and standards, please refer to the [Release Standards documentation](https://veracode.atlassian.net/wiki/spaces/DEVOPS/pages/**********/Release+Standards).

## External Secrets Migration

This chart has been updated to use the new External Secrets Operator API version `external-secrets.io/v1` instead of the deprecated `kubernetes-client.io/v1`.

### Key Changes

1. **API Version**: Updated from `kubernetes-client.io/v1` to `external-secrets.io/v1`
2. **SecretStore**: The chart now creates a `SecretStore` resource for each `ExternalSecret`
3. **Structure**: Provider configuration (AWS Secrets Manager) is now defined in the `SecretStore`

### Configuration

The existing `external_secret` configuration in your values files remains compatible. The chart automatically:

- Creates a `SecretStore` resource with AWS Secrets Manager configuration
- Creates an `ExternalSecret` that references the `SecretStore`
- Maintains the same secret data structure

### Example Configuration

```yaml
external_secret:
  name: integrations-secrets
  region: us-east-1  # Optional, defaults to us-east-1
  # Optional: AWS role to assume
  # roleArn: arn:aws:iam::************:role/external-secrets-role
  # Optional: Authentication configuration
  # auth:
  #   secretRef:
  #     accessKeyIDSecretRef:
  #       name: aws-credentials
  #       key: access-key-id
  #     secretAccessKeySecretRef:
  #       name: aws-credentials
  #       key: secret-access-key
  data:
  - key: dev/shared/integrations-secrets
    name: SECURITY_PASSWORD
    property: SECURITY_PASSWORD
  - key: dev/shared/integrations-secrets
    name: SECURITY_SALT
    property: SECURITY_SALT
```

### Authentication Methods

The chart supports multiple AWS authentication methods:

1. **Pod Identity** (Recommended): Uses IRSA (IAM Roles for Service Accounts)
2. **Access Keys**: Store AWS credentials in a Kubernetes secret
3. **Assume Role**: Assume a specific IAM role for secret access

For Pod Identity, ensure your service account has the appropriate IAM role annotation:

```yaml
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/integrations-serviceaccount
```